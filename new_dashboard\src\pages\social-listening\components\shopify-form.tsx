/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-misused-promises */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToShopify } from '../utils';
import { channelNames, shopifyIntegrationSteps } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { ModernIntegrationWrapper } from './integration-components/modern-integration-wrapper';
import { ShopifySellerPanel } from './integration-components/shopify-panel';
//import StoreConnectionSteps from './woocommerce-steps';
//import CommerceIntegrationLayout from './commerce-integration-layout';
import image from '../images/integrations/shopify.png';
//import Input from './Input';
import { AuthUser } from '../../../types/auth';

interface FormFields {
   channelName: string;
   storeName: string;
   apiKey: string;
   apiSecret: string;
   adminAccessToken: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

/*const SellerPanel: React.FC<{
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => Promise<void>;
   storeName: string;
   apiKey: string;
   apiSecret: string;
   adminAccessToken: string;
   apiError: ApiError | null;
}> = ({
   onChange,
   trying,
   onConnect,
   storeName,
   apiKey,
   apiSecret,
   adminAccessToken,
   apiError,
}) => (
   <div className='seller-panel'>
      <div className='seller-panel__headings'>
         <h5 className='title'>Seller Panel</h5>
         <p className='description'>
            Please provide the following credentials for Shopify:
         </p>
      </div>
      <form className='seller-panel__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               type='text'
               id='store-name'
               label='Store Name'
               placeholder='Enter your store name...'
               name='storeName'
               value={storeName}
               onChange={onChange}
               extra
               extraValue='.myshopify.com'
            />

            <Input
               type='password'
               id='api-key'
               label='API Key'
               placeholder='eg. xxxxxxxx'
               name='apiKey'
               value={apiKey}
               onChange={onChange}
            />
            <Input
               type='password'
               id='api-secret'
               label='API Secret'
               placeholder='eg. xxxxxxxx'
               name='apiSecret'
               value={apiSecret}
               onChange={onChange}
            />
            <Input
               type='password'
               id='admin-access-token'
               label='Admin Access Token'
               placeholder='eg. xxxxxxxx'
               name='adminAccessToken'
               value={adminAccessToken}
               onChange={onChange}
            />
         </div>
         {apiError && apiError.message && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);*/

const ShopifyForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: channelNames.SHOPIFY,
      storeName: '',
      apiKey: '',
      apiSecret: '',
      adminAccessToken: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      async (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { apiSecret, apiKey, storeName, adminAccessToken } = formFields;
         try {
            setTrying(true);
            setApiError({
               success: false,
               message: '',
            });

            await connectDisconnectToShopify({
               channel_name: channelNames.SHOPIFY,
               client_id,
               channel_client_key: apiKey,
               channel_secret: apiSecret,
               admin_access_token: adminAccessToken,
               store_url: `${storeName}.myshopify.com`,
               isConnect: true,
            });
            setFormFields(defaultState);
            setApiError({
               success: true,
               message: 'Connection Established, Redirecting...',
            });
            setTimeout(() => {
               navigate('/integrations');
            }, 3000);
         } catch (err) {
            const error = err as any;

            const errMessage =
               error.response?.data?.message || 'Error connecting to Shopify';
            setApiError({
               success: false,
               message: errMessage,
            });
         } finally {
            setTrying(false);
         }
      },
      [formFields, history, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   /*const leftContent = <StoreConnectionSteps steps={shopifyIntegrationSteps} />;*/

   return (
      <ModernIntegrationWrapper 
      title="Shopify Integration "
         description="Connect your Shopify account to manage your logistics seamlessly"
         logo={image}
         logoAlt="Shopify Logo"
         steps={shopifyIntegrationSteps}
      >
         <ShopifySellerPanel
            title="Account Credentials"
            description="Please provide your Shiprocket API credentials to establish the connection"
            storeName={formFields.storeName}
            apiKey={formFields.apiKey}
            apiSecret={formFields.apiSecret} 
            adminAccessToken={formFields.adminAccessToken}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText="Connect to Shiprocket"
            loadingText="Connecting to Shiprocket..."
         />
      </ModernIntegrationWrapper>
   );
};

export default ShopifyForm;
